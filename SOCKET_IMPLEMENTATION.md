# Super Simple EventEmitter Socket.IO Implementation

This is an extremely simple Socket.IO implementation using Node.js EventEmitter that replaces the 30-second polling interval with real-time notifications.

## How It Works

### Backend
1. **EventEmitter Module** (`backend/modules/ioEmitter.js`):
   - Simple Node.js EventEmitter instance
   - No complexity, just emit events

2. **Socket Service** (`backend/services/socketService.js`):
   - Listens to EventEmitter events
   - Broadcasts to all connected Socket.IO clients
   - No authentication, no rooms, no complexity

### Frontend
- **Simple Socket Hook** (`frontend/src/hooks/useSocket.js`):
  - Global socket instance
  - Connects automatically when enabled via environment variable
  - Simple `getSocket()` function to get the socket instance

### Environment Control
- **Frontend Environment** (`frontend/.env.local`):
  ```
  NEXT_PUBLIC_BACKEND_URL=http://localhost:5000
  NEXT_PUBLIC_ENABLE_SOCKET=true
  ```
  - Set `NEXT_PUBLIC_ENABLE_SOCKET=false` to disable Socket.IO (won't conflict with PWA)
  - Set `NEXT_PUBLIC_ENABLE_SOCKET=true` to enable real-time updates

## Events

### 1. `notifications/changed`
- **Triggered when**: New notification created, notification read, notification archived
- **Frontend action**: Refetches unread count and notifications list
- **Data**: `{ action: 'created|read|archived', notificationID: number }`

### 2. `time/updated`
- **Triggered when**: Delivery/Installer updates their time
- **Frontend action**: Refetches unread count (for new time update notifications)
- **Data**: `{ role: 'delivery|installer', purposeId: number, quotationNo: string, clientName: string }`

## Usage

### Backend - Emit Changes
```javascript
const ioEmitter = require('../modules/ioEmitter');

// Emit notification changes
ioEmitter.emit('notifyAll', {
    name: 'notifications/changed',
    data: { action: 'created', notificationID: 123 }
});

// Emit time updates
ioEmitter.emit('notifyAll', {
    name: 'time/updated',
    data: { role: 'delivery', purposeId: 456 }
});
```

### Frontend - Listen for Changes
```javascript
import { getSocket } from '@src/hooks/useSocket';

useEffect(() => {
    const socket = getSocket();
    if (!socket) return;

    const handleNotificationsChanged = (data) => {
        console.log('Notifications changed:', data);
        fetchUnreadCount();
    };

    socket.on('notifications/changed', handleNotificationsChanged);

    return () => {
        socket.off('notifications/changed', handleNotificationsChanged);
    };
}, []);
```

## Benefits

1. **Simple**: No complex authentication, rooms, or user management
2. **Reliable**: Just broadcasts to all clients, they decide what to refetch
3. **PWA Compatible**: Can be easily disabled via environment variable
4. **Easy to Debug**: Minimal code, easy to understand
5. **Scalable**: Broadcasting is efficient for small to medium applications

## Fallback

If Socket.IO is disabled or fails:
- Frontend continues to work normally
- No real-time updates, but manual refresh still works
- No errors or conflicts with PWA functionality

## Files Modified

### Backend
- `backend/services/socketService.js` - Simple socket service
- `backend/services/notificationService.js` - Added change emissions
- `backend/routes/client.js` - Added time update emissions
- `backend/index.js` - Socket.IO server initialization

### Frontend
- `frontend/src/hooks/useSocketSimple.js` - Simple socket hook
- `frontend/src/app/provider/NotificationContext.js` - Listen for changes and refetch
- `frontend/.env.local` - Environment configuration

## Testing

1. **Enable Socket.IO**: Set `NEXT_PUBLIC_ENABLE_SOCKET=true`
2. **Start Backend**: `npm run dev` in backend folder
3. **Start Frontend**: `npm run dev` in frontend folder
4. **Test**: Create notifications or update delivery/installer times
5. **Observe**: Real-time updates without page refresh

## Disable Socket.IO

To disable Socket.IO (for PWA compatibility):
1. Set `NEXT_PUBLIC_ENABLE_SOCKET=false` in `frontend/.env.local`
2. Application will work normally without real-time updates
3. Users can manually refresh to see new notifications
