const endpointsDB = [
    {
        "_id": "66f2b3765fc1d8f82af7c728",
        "endpoint_id": 101,
        "name": "FETCH_TOKEN",
        "category": "USERS",
        "is_public": true,
        "is_accessible": true
    },
    {
        "_id": "66f2b3b25fc1d8f82af7c729",
        "endpoint_id": 102,
        "name": "FETCH_USER",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "66f2b3d45fc1d8f82af7c72a",
        "endpoint_id": 103,
        "name": "FETCH_USERS_LIST",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2b3e05fc1d8f82af7c72b",
        "endpoint_id": 104,
        "name": "CREATE_USER",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2b3fa5fc1d8f82af7c72d",
        "endpoint_id": 105,
        "name": "UPDATE_USER_ROLE",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2b4095fc1d8f82af7c72e",
        "endpoint_id": 106,
        "name": "DELETE_USER",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2b41e5fc1d8f82af7c72f",
        "endpoint_id": 107,
        "name": "FETCH_PASSWORD_RESET_TOKEN",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "66f2b42d5fc1d8f82af7c730",
        "endpoint_id": 108,
        "name": "UPDATE_PASSWORD",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "66f2b45d5fc1d8f82af7c731",
        "endpoint_id": 201,
        "name": "FETCH_STREAMS_LIST",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "66f2b46a5fc1d8f82af7c732",
        "endpoint_id": 202,
        "name": "FETCH_STREAM_URL",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "66f2e56d5fc1d8f82af7c73d",
        "endpoint_id": 301,
        "name": "FETCH_REGIONS",
        "category": "REGIONS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "66f2e5a05fc1d8f82af7c73e",
        "endpoint_id": 401,
        "name": "FETCH_COORDINATES",
        "category": "VESSEL LOCATIONS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "66f2e5c15fc1d8f82af7c73f",
        "endpoint_id": 501,
        "name": "FETCH_ARTIFACTS",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "66f2e5da5fc1d8f82af7c740",
        "endpoint_id": 601,
        "name": "FETCH_ROLES",
        "category": "ROLES",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e5e55fc1d8f82af7c741",
        "endpoint_id": 602,
        "name": "CREATE_ROLE",
        "category": "ROLES",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e5f05fc1d8f82af7c742",
        "endpoint_id": 603,
        "name": "UPDATE_ROLE",
        "category": "ROLES",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e5fb5fc1d8f82af7c743",
        "endpoint_id": 604,
        "name": "DELETE_ROLE",
        "category": "ROLES",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e63a5fc1d8f82af7c744",
        "endpoint_id": 701,
        "name": "FETCH_PERMISSIONS",
        "category": "PERMISSIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e6655fc1d8f82af7c745",
        "endpoint_id": 801,
        "name": "FETCH_SESSION_LOGS",
        "category": "LOGS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e6745fc1d8f82af7c746",
        "endpoint_id": 802,
        "name": "FETCH_SESSION_LOG_BY_ID",
        "category": "LOGS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2e6f85fc1d8f82af7c747",
        "endpoint_id": 901,
        "name": "FETCH_FILE_URL",
        "category": "STORAGE",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "6707ddb2af2c1b345bd06b91",
        "endpoint_id": 1001,
        "name": "FETCH_STATISTICS",
        "category": "STATISTICS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "672cf64d884521382b8e8f41",
        "endpoint_id": 1101,
        "name": "FETCH_VESSELS_INFO",
        "category": "VESSELS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "672d130b884521382b8e8f44",
        "endpoint_id": 1201,
        "name": "SEND_OTP",
        "category": "OTP VERIFICATION",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "672d1318884521382b8e8f45",
        "endpoint_id": 1202,
        "name": "VERIFY_OTP",
        "category": "OTP VERIFICATION",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "673760d764f4eeb4153730f8",
        "endpoint_id": 605,
        "name": "REORDER_ROLE",
        "category": "ROLES",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6744bb0f945c9edcfd986614",
        "endpoint_id": 109,
        "name": "INVITE_USER",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6744bb2a945c9edcfd986615",
        "endpoint_id": 110,
        "name": "VERIFY_INVITE_USER",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "676ec6a2d6a1116927e87318",
        "endpoint_id": 1301,
        "name": "FETCH_LOCATION",
        "category": "GEOLOCATION",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "678e8824f18fcea2672946f2",
        "endpoint_id": 502,
        "name": "FETCH_PAGINATED_ARTIFACTS",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "678e8838f18fcea2672946f3",
        "endpoint_id": 503,
        "name": "FETCH_ARTIFACT_FILTERS",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "678fdce35e08339c7bdb1b03",
        "endpoint_id": 1401,
        "name": "FETCH_TOUR_GUIDE",
        "category": "TOUR GUIDE",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "678fdd775e08339c7bdb1b04",
        "endpoint_id": 1402,
        "name": "CREATE_TOUR_GUIDE",
        "category": "TOUR GUIDE",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "678fdd9a5e08339c7bdb1b05",
        "endpoint_id": 1403,
        "name": "UPDATE_TOUR_GUIDE",
        "category": "TOUR GUIDE",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67922f6a7524efb8a6e0cf5b",
        "endpoint_id": 1501,
        "name": "FETCH_NOTIFICATION_ALERTS",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6792305e7524efb8a6e0cf5c",
        "endpoint_id": 1502,
        "name": "CREATE_NOTIFICATION_ALERTS",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "679230f07524efb8a6e0cf5d",
        "endpoint_id": 1503,
        "name": "DELETE_NOTIFICATION_ALERTS",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "679231137524efb8a6e0cf5e",
        "endpoint_id": 1504,
        "name": "UPDATE_NOTIFICATION_ALERTS",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "679231407524efb8a6e0cf5f",
        "endpoint_id": 1505,
        "name": "FETCH_NOTIFICATION_ALERTS_BY_ID",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67924d927524efb8a6e0cf6d",
        "endpoint_id": 1601,
        "name": "FETCH_IN_APP_NOTIFICATIONS",
        "category": "IN APP NOTIFICATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "679250b07524efb8a6e0cf6f",
        "endpoint_id": 1602,
        "name": "MARK_AS_READ_IN_APP_NOTIFICATIONS",
        "category": "IN APP NOTIFICATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a0b9bcfe86a95584d3cc7d",
        "endpoint_id": 1701,
        "name": "FETCH_NOTIFICATION_SUMMARIES",
        "category": "NOTIFICATION SUMMARY",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a0ba5efe86a95584d3cc7e",
        "endpoint_id": 1702,
        "name": "FETCH_NOTIFICATION_SUMMARIES_BY_ID",
        "category": "NOTIFICATION SUMMARY",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a0ba85fe86a95584d3cc7f",
        "endpoint_id": 1703,
        "name": "CREATE_NOTIFICATION_SUMMARIES",
        "category": "NOTIFICATION SUMMARY",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a0babcfe86a95584d3cc80",
        "endpoint_id": 1704,
        "name": "DELETE_NOTIFICATION_SUMMARIES",
        "category": "NOTIFICATION SUMMARY",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a0baebfe86a95584d3cc81",
        "endpoint_id": 1705,
        "name": "UPDATE_NOTIFICATION_SUMMARIES",
        "category": "NOTIFICATION SUMMARY",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67a4fb010dc2628addc4e666",
        "endpoint_id": 1506,
        "name": "UNSUBSCRIBE_NOTIFICATION_ALERTS",
        "category": "NOTIFICATION ALERTS",
        "is_public": true,
        "is_acccessible": true
    },
    {
        "_id": "67a501eb0dc2628addc4e66a",
        "endpoint_id": 1706,
        "name": "UNSUBSCRIBE_NOTIFICATION_SUMMARIES",
        "category": "NOTIFICATION SUMMARY",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "67af634425484eb3073fbf37",
        "name": "FETCH_NLP_SUGGESTIONS",
        "category": "ARTIFACTS COMPLETIONS",
        "is_public": false,
        "is_accessible": false,
        "endpoint_id": 504
    },
    {
        "_id": "67bf5ff4c6fa8f4422f79730",
        "endpoint_id": 111,
        "name": "UPDATE_USER_ALLOWED_UNITS",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67cf3ae5235f7126428de2b7",
        "endpoint_id": 902,
        "name": "FETCH_BATCH_URLS",
        "category": "STORAGE",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "67cf3ded235f7126428de2b8",
        "endpoint_id": 803,
        "name": "FETCH_SESSION_LOGS_BY_USER",
        "category": "LOGS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d446e84e63cf958bfbb6c1",
        "endpoint_id": 1901,
        "name": "FETCH_ALLOWED_EMAIL_DOMAINS",
        "category": "EMAIL DOMAINS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86c11926673c75a3da109",
        "endpoint_id": 2001,
        "name": "FETCH_REGION_GROUPS",
        "category": "REGION GROUPS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86c7c926673c75a3da10a",
        "endpoint_id": 2002,
        "name": "CREATE_REGION_GROUP",
        "category": "REGION GROUPS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86cb0926673c75a3da10b",
        "endpoint_id": 2003,
        "name": "UPDATE_REGION_GROUP",
        "category": "REGION GROUPS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86ccb926673c75a3da10c",
        "endpoint_id": 2004,
        "name": "DELETE_REGION_GROUP",
        "category": "REGION GROUPS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86e22cbb793bdb44d3a88",
        "endpoint_id": 1801,
        "name": "CREATE_FAVOURITE_ARTIFACT",
        "category": "FAVOURITE ARTIFACTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86e36cbb793bdb44d3a89",
        "endpoint_id": 1802,
        "name": "FETCH_ALL_FAVOURITE_ARTIFACTS",
        "category": "FAVOURITE ARTIFACTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86e47cbb793bdb44d3a8a",
        "endpoint_id": 1803,
        "name": "DELETE_FAVOURITE_ARTIFACT",
        "category": "FAVOURITE ARTIFACTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67d86e63cbb793bdb44d3a8b",
        "endpoint_id": 1804,
        "name": "FETCH_SINGLE_FAVOURITE_ARTIFACT",
        "category": "FAVOURITE ARTIFACTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e44369f02d5c007eb4a170",
        "endpoint_id": 2101,
        "name": "FETCH_ORGANIZATIONS",
        "category": "ORGANIZATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e443fcf02d5c007eb4a171",
        "endpoint_id": 2102,
        "name": "FETCH_ORGANIZATION_BY_ID",
        "category": "ORGANIZATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e44431f02d5c007eb4a172",
        "endpoint_id": 2103,
        "name": "CREATE_ORGANIZATION",
        "category": "ORGANIZATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e4443ef02d5c007eb4a173",
        "endpoint_id": 2104,
        "name": "UPDATE_ORGANIZATION",
        "category": "ORGANIZATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e44464f02d5c007eb4a174",
        "endpoint_id": 2105,
        "name": "DELETE_ORGANIZATION",
        "category": "ORGANIZATIONS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67e44490f02d5c007eb4a175",
        "endpoint_id": 112,
        "name": "UPDATE_USER_ORGANIZATION",
        "category": "USERS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67ecf4223433448c1c08d93e",
        "endpoint_id": 203,
        "name": "GET_CLIP",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "67ecf4223433448c1c08d93c",
        "endpoint_id": 204,
        "name": "GET_SCREENSHOT",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "67f6c2448a24775155418da2",
        "endpoint_id": 1509,
        "name": "FETCH_NOTIFICATION_ALERTS_BY_USER",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "66f2b42d5fc1d8f82af7c731",
        "endpoint_id": 113,
        "name": "UPDATE_USER_SETTINGS",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "68272c404d387cbf8ff25a58",
        "endpoint_id": 205,
        "name": "FETCH_STREAM_URL_V2",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "682b507e0237739828212d46",
        "endpoint_id": 2201,
        "name": "FETCH_ALL_HOME_PORTS",
        "category": "HOME PORTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "6839ba78ca03614f75678bf8",
        "endpoint_id": 2301,
        "name": "FETCH_VESSEL_MANAGEMENT",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6839ba78ca03614f75678bf9",
        "endpoint_id": 2302,
        "name": "FETCH_VESSEL_MANAGEMENT_BY_ID",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6839ba78ca03614f75678bfa",
        "endpoint_id": 2303,
        "name": "CREATE_VESSEL_MANAGEMENT",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6839ba78ca03614f75678bfb",
        "endpoint_id": 2304,
        "name": "UPDATE_VESSEL_MANAGEMENT",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6839ba78ca03614f75678bfc",
        "endpoint_id": 2305,
        "name": "FETCH_VESSEL_UNIT_IDS",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "6839bacfca03614f75678bfd",
        "endpoint_id": 903,
        "name": "FETCH_CLOUDFRONT_SIGNED_URL",
        "category": "STORAGE",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "68499071e407acdb88a989a8",
        "endpoint_id": 2307,
        "name": "FETCH_ALL_VESSEL_MANAGEMENT",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "68499080e407acdb88a989a9",
        "endpoint_id": 2306,
        "name": "FETCH_ASSIGNED_UNIT_IDS",
        "category": "VESSEL MANAGEMENT",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "684990c2e407acdb88a989aa",
        "endpoint_id": 1102,
        "name": "FETCH_VESSELS_INFO_V2",
        "category": "VESSELS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "6849914ee407acdb88a989ab",
        "endpoint_id": 402,
        "name": "FETCH_COORDINATES_V2",
        "category": "VESSEL LOCATIONS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "68499836e407acdb88a989ad",
        "endpoint_id": 206,
        "name": "FETCH_STREAM_DASH_URL",
        "category": "STREAMS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "68499c81e407acdb88a989ae",
        "endpoint_id": 505,
        "name": "DOWNLOAD_ARTIFACT",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "68499c94e407acdb88a989af",
        "endpoint_id": 506,
        "name": "FETCH_ARTIFACTS_V2",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "68499ca5e407acdb88a989b0",
        "endpoint_id": 507,
        "name": "FETCH_HOURS_AGGREGATED_COUNT",
        "category": "VESSEL ARTIFACTS",
        "is_public": false,
        "is_accessible": true
    },
    {
        "_id": "68499d1ae407acdb88a989b1",
        "endpoint_id": 114,
        "name": "FETCH_USERS_LIST_V2",
        "category": "USERS",
        "is_public": true,
        "is_accessible": false
    },
    {
        "_id": "68499d69e407acdb88a989b2",
        "endpoint_id": 1507,
        "name": "FETCH_MAP_FOR_ALERT",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "68499d77e407acdb88a989b3",
        "endpoint_id": 1508,
        "name": "FETCH_URL_FOR_ALERT",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "68499d90e407acdb88a989b4",
        "endpoint_id": 1510,
        "name": "FETCH_MAP_CLUSTER",
        "category": "NOTIFICATION ALERTS",
        "is_public": false,
        "is_accessible": false
    },
    {
        "_id": "68499debe407acdb88a989b5",
        "endpoint_id": 1707,
        "name": "FETCH_MAP_FOR_SUMMARIES_V2",
        "category": "NOTIFICATION SUMMARY",
        "is_public": true,
        "is_accessible": false
    }
];

const endpointIds = {
    FETCH_TOKEN: 101,
    FETCH_USER: 102,
    FETCH_USERS_LIST: 103,
    CREATE_USER: 104,
    UPDATE_USER_ROLE: 105,
    DELETE_USER: 106,
    FETCH_PASSWORD_RESET_TOKEN: 107,
    UPDATE_PASSWORD: 108,
    INVITE_USER: 109,
    VERIFY_INVITE_USER: 110,
    UPDATE_USER_ALLOWED_UNITS: 111,
    UPDATE_USER_ORGANIZATION: 112,
    UPDATE_USER_SETTINGS: 113,
    FETCH_USERS_LIST_V2: 114,

    FETCH_STREAMS_LIST: 201,
    FETCH_STREAM_URL: 202,
    GET_CLIP: 203,
    GET_SCREENSHOT: 204,
    FETCH_STREAM_URL_V2: 205,
    FETCH_STREAM_DASH_URL: 206,

    FETCH_REGIONS: 301,

    FETCH_COORDINATES: 401,
    FETCH_COORDINATES_V2: 402,

    FETCH_ARTIFACTS: 501,
    FETCH_PAGINATED_ARTIFACTS: 502,
    FETCH_ARTIFACT_FILTERS: 503,
    FETCH_NLP_SUGGESTIONS: 504,
    DOWNLOAD_ARTIFACT: 505,
    FETCH_ARTIFACTS_V2: 506,
    FETCH_HOURS_AGGREGATED_COUNT: 507,

    FETCH_ROLES: 601,
    CREATE_ROLE: 602,
    UPDATE_ROLE: 603,
    DELETE_ROLE: 604,
    REORDER_ROLE: 605,

    FETCH_PERMISSIONS: 701,

    FETCH_SESSION_LOGS: 801,
    FETCH_SESSION_LOG_BY_ID: 802,
    FETCH_SESSION_LOGS_BY_USER: 803,

    FETCH_FILE_URL: 901,
    FETCH_BATCH_URLS: 902,
    FETCH_CLOUDFRONT_SIGNED_URL: 903,

    FETCH_STATISTICS: 1001,

    FETCH_VESSELS_INFO: 1101,
    FETCH_VESSELS_INFO_V2: 1102,

    SEND_OTP: 1201,
    VERIFY_OTP: 1202,
    FETCH_LOCATION: 1301,

    FETCH_TOUR_GUIDE: 1401,
    CREATE_TOUR_GUIDE: 1402,
    UPDATE_TOUR_GUIDE: 1403,

    FETCH_NOTIFICATION_ALERTS: 1501,
    CREATE_NOTIFICATION_ALERTS: 1502,
    DELETE_NOTIFICATION_ALERTS: 1503,
    UPDATE_NOTIFICATION_ALERTS: 1504,
    FETCH_NOTIFICATION_ALERTS_BY_ID: 1505,
    UNSUBSCRIBE_NOTIFICATION_ALERTS: 1506,
    FETCH_MAP_FOR_ALERT: 1507,
    FETCH_URL_FOR_ALERT: 1508,
    FETCH_NOTIFICATION_ALERTS_BY_USER: 1509,
    FETCH_MAP_CLUSTER: 1510,

    FETCH_IN_APP_NOTIFICATIONS: 1601,
    MARK_AS_READ_IN_APP_NOTIFICATIONS: 1602,

    FETCH_NOTIFICATION_SUMMARIES: 1701,
    FETCH_NOTIFICATION_SUMMARIES_BY_ID: 1702,
    CREATE_NOTIFICATION_SUMMARIES: 1703,
    DELETE_NOTIFICATION_SUMMARIES: 1704,
    UPDATE_NOTIFICATION_SUMMARIES: 1705,
    UNSUBSCRIBE_NOTIFICATION_SUMMARIES: 1706,
    FETCH_MAP_FOR_SUMMARIES_V2: 1707,

    CREATE_FAVOURITE_ARTIFACT: 1801,
    FETCH_ALL_FAVOURITE_ARTIFACTS: 1802,
    DELETE_FAVOURITE_ARTIFACT: 1803,
    FETCH_USER_FAVOURITE_ARTIFACTS: 1804,

    FETCH_ALLOWED_EMAIL_DOMAINS: 1901,

    FETCH_REGION_GROUPS: 2001,
    CREATE_REGION_GROUP: 2002,
    UPDATE_REGION_GROUP: 2003,
    DELETE_REGION_GROUP: 2004,

    FETCH_ORGANIZATIONS: 2101,
    FETCH_ORGANIZATION_BY_ID: 2102,
    CREATE_ORGANIZATION: 2103,
    UPDATE_ORGANIZATION: 2104,
    DELETE_ORGANIZATION: 2105,

    FETCH_ALL_HOME_PORTS: 2201,

    FETCH_PAGINATED_VESSEL_MANAGEMENT: 2301,
    FETCH_VESSEL_MANAGEMENT_BY_ID: 2302,
    CREATE_VESSEL_MANAGEMENT: 2303,
    UPDATE_VESSEL_MANAGEMENT: 2304,
    FETCH_VESSEL_UNIT_IDS: 2305,
    FETCH_ASSIGNED_UNIT_IDS: 2306,
    FETCH_ALL_VESSEL_MANAGEMENT: 2307,
};

const apikeys = [
    {
        "_id": "672cd30b450b37b799977561",
        "allowed_endpoints": [],
        "api_key": "3a8f8871ea8ab755eb2372a86c721495"
    },
    {
        "_id": "676573a109aa0adb1b6a8387",
        "allowed_endpoints": [
            201,
            202,
            401,
            501,
            1001,
            1101,
            301
        ],
        "api_key": "e9d992f3727f42a7d9472529a1205144"
    },
    {
        "_id": "678001ea160c8a98ba8444c3",
        "allowed_endpoints": [
            101,
            102,
            103,
            104,
            105,
            106,
            107,
            108,
            109,
            110,
            201,
            202,
            301,
            401,
            601,
            602,
            603,
            604,
            605,
            501,
            1301,
            1101,
            1001,
            801,
            802,
            701
        ],
        "api_key": "daa5e5d912c97bd5dc6c06040b8525a0"
    },
    {
        "_id": "6790ffb4a988272f99a5b0cc",
        "allowed_endpoints": [
            201,
            202,
            1101,
            1301,
            401,
            1001
        ],
        "api_key": "80016655b3ca75685db487daa8a1ea46"
    },
    {
        "_id": "6790ffe7a988272f99a5b0db",
        "allowed_endpoints": [
            201,
            202,
            401,
            1001,
            1101,
            1301
        ],
        "api_key": "f5fa8e017df41235be2697ccf57b7fb4"
    },
    {
        "_id": "67a10bac73d382c912b9275b",
        "allowed_endpoints": [
            201,
            202,
            401,
            501,
            502,
            503,
            1001,
            1101,
            1301,
            1601,
            1602,
            1501,
            1502,
            1503,
            1504,
            1505,
            1506,
            1701,
            1702,
            1703,
            1704,
            1705,
            204,
            203,
            301,
            901,
            902
        ],
        "api_key": "c88fd19c8a68452cc051bb3d9c0516a6"
    },
    {
        "_id": "67e4420c9e837aa171dfef6f",
        "allowed_endpoints": [
            201,
            202,
            401,
            501,
            502,
            503,
            1001,
            1101,
            1301,
            1501,
            1505,
            301,
            1601,
            1602,
            2001,
            2002,
            2003,
            2004,
            1801,
            1802,
            1803,
            1804,
            901,
            902,
            1701,
            1702,
            2201,
            903,
            204,
            203,
            205
        ],
        "api_key": "c930ad4de04091701344de41135f25eb"
    },
    {
        "_id": "67e8610ec8ef8b73608895ed",
        "allowed_endpoints": [
            201,
            202,
            301,
            401,
            501,
            502,
            503,
            701,
            901,
            902,
            1001,
            1101,
            1301,
            1601,
            1602,
            504,
            1801,
            1802,
            1803,
            1804
        ],
        "api_key": "34d8c64ac16dd2ca854e1a6dbb65a1db"
    },
    {
        "_id": "6842540067b0048d5fb65285",
        "allowed_endpoints": [
            201,
            202,
            203,
            204,
            205,
            301,
            401,
            501,
            502,
            503,
            901,
            902,
            903,
            1001,
            1101,
            1301,
            2201
        ],
        "api_key": "7e3e7b4fe026645549e52462c96dad73"
    }
];

const concatenateAllowedEndpointsWithoutRepeatation = (apikeys) => {
    const concatenatedEndpoints = [];
    apikeys.forEach(apikey => {
        apikey.allowed_endpoints.forEach(endpoint => {
            if (!concatenatedEndpoints.includes(endpoint)) {
                concatenatedEndpoints.push(endpoint);
            }
        });
    });
    return concatenatedEndpoints;
}

const allowedEndpoints = concatenateAllowedEndpointsWithoutRepeatation(apikeys);
const notInDB = [];
const inDB = [];

for (const key in endpointIds) {
    if (!endpointsDB.find(endpoint => endpoint.endpoint_id === endpointIds[key])) {
        notInDB.push(key);
    } else {
        inDB.push(key);
    }
}

console.log("Not in DB:", notInDB);
// console.log("Allowed Endpoints:", allowedEndpoints);
// console.log("Endpoints in DB But not in Allowed Endpoints:", endpointsDB.filter(endpoint => allowedEndpoints.includes(endpoint.endpoint_id)));
// console.log("In DB:", inDB);