import withPWA from "next-pwa";

// Define Next.js settings
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
};

// Define PWA options for `next-pwa`
const pwaConfig = {
  dest: "public", // Where service worker and PWA files are generated
  disable: process.env.NODE_ENV === "development", // Only enable in production
  register: true,
  skipWaiting: true,
  buildExcludes: [/middleware-manifest\.json$/], // Exclude middleware manifest
};

// Export the configuration with `withPWA`, applying `pwaConfig` only to `next-pwa`
export default withPWA(pwaConfig)(nextConfig);
