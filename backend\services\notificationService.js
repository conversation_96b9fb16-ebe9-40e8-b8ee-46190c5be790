const { sql, getPool } = require('../db');
const ioEmitter = require('../modules/ioEmitter');

class NotificationServiceSimple {
    /**
     * Create a notification and send it to recipients
     * Simple version without advanced SQL features
     */
    async createNotification(notificationData) {
        const pool = await getPool();
        const transaction = new sql.Transaction(pool);

        try {
            await transaction.begin();
            const request = new sql.Request(transaction);

            const templateQuery = `
                SELECT nt.ID as TemplateID, nt.TitleTemplate, nt.MessageTemplate, 
                       nt.ActionButtonText, nt.ActionUrl, nt.Priority, ntype.ID as TypeID
                FROM NotificationTemplates nt
                INNER JOIN NotificationTypes ntype ON nt.TypeID = ntype.ID
                WHERE nt.TemplateName = @templateName AND nt.IsActive = 1`;
            
            request.input('templateName', sql.NVarChar(100), notificationData.templateName);
            const templateResult = await request.query(templateQuery);

            if (templateResult.recordset.length === 0) {
                throw new Error(`Template '${notificationData.templateName}' not found`);
            }

            const template = templateResult.recordset[0];

            const title = this.replacePlaceholders(template.TitleTemplate, notificationData.context);
            const message = this.replacePlaceholders(template.MessageTemplate, notificationData.context);
            const actionUrl = template.ActionUrl ? this.replacePlaceholders(template.ActionUrl, notificationData.context) : null;

            const notificationQuery = `
                INSERT INTO Notifications (TypeID, TemplateID, Title, Message, ActionButtonText, ActionUrl, 
                                         Priority, SenderUserID, SenderEmployeeID, ContextData, ReferenceID, 
                                         ReferenceType, ExpiresAt)
                VALUES (@typeID, @templateID, @title, @message, @actionButtonText, @actionUrl, 
                        @priority, @senderUserID, @senderEmployeeID, @contextData, @referenceID, 
                        @referenceType, @expiresAt);
                SELECT SCOPE_IDENTITY() as NotificationID;`;

            request.input('typeID', sql.Int, template.TypeID);
            request.input('templateID', sql.Int, template.TemplateID);
            request.input('title', sql.NVarChar(255), title);
            request.input('message', sql.NVarChar(1000), message);
            request.input('actionButtonText', sql.NVarChar(50), template.ActionButtonText);
            request.input('actionUrl', sql.NVarChar(255), actionUrl);
            request.input('priority', sql.TinyInt, template.Priority);
            request.input('senderUserID', sql.NVarChar(32), notificationData.senderUserID);
            request.input('senderEmployeeID', sql.NVarChar(32), notificationData.senderEmployeeID);
            request.input('contextData', sql.NVarChar(sql.MAX), JSON.stringify(notificationData.context));
            request.input('referenceID', sql.NVarChar(50), notificationData.referenceID);
            request.input('referenceType', sql.NVarChar(50), notificationData.referenceType);
            request.input('expiresAt', sql.DateTime2, notificationData.expiresAt);

            const notificationResult = await request.query(notificationQuery);
            const notificationID = notificationResult.recordset[0].NotificationID;

            for (const recipient of notificationData.recipients) {
                const recipientQuery = `
                    INSERT INTO NotificationRecipients (NotificationID, RecipientUserID, RecipientRoleID, RecipientEmployeeID)
                    VALUES (@notificationID, @recipientUserID, @recipientRoleID, @recipientEmployeeID)`;

                const recipientRequest = new sql.Request(transaction);
                recipientRequest.input('notificationID', sql.BigInt, notificationID);
                recipientRequest.input('recipientUserID', sql.NVarChar(32), recipient.userID || null);
                recipientRequest.input('recipientRoleID', sql.Int, recipient.roleID || null);
                recipientRequest.input('recipientEmployeeID', sql.NVarChar(32), recipient.employeeID || null);

                await recipientRequest.query(recipientQuery);
            }

            await transaction.commit();

            // Emit notification change event
            ioEmitter.emit('notifyAll', {
                name: 'notifications/changed',
                data: { action: 'created', data: notificationData }
            });

            return notificationID;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    /**
     * Replace placeholders in template strings
     */
    replacePlaceholders(template, context) {
        return template.replace(/\{(\w+)\}/g, (match, key) => {
            return context[key] !== undefined ? context[key] : match;
        });
    }

    /**
     * Get notifications for a specific user - Simple version
     */
    async getUserNotifications(userID, options = {}) {
        const pool = await getPool();
        const request = new sql.Request(pool);

        const {
            limit = 50,
            offset = 0,
            includeRead = true,
            includeArchived = false,
            typeFilter = null
        } = options;

        let whereClause = 'WHERE (nr.RecipientUserID = @userID OR nr.RecipientRoleID = (SELECT RoleID FROM users WHERE id = @userID))';
        
        if (!includeRead) {
            whereClause += ' AND nr.IsRead = 0';
        }
        
        if (!includeArchived) {
            whereClause += ' AND nr.IsArchived = 0';
        }
        
        if (typeFilter) {
            whereClause += ' AND nt.TypeName = @typeFilter';
            request.input('typeFilter', sql.NVarChar(50), typeFilter);
        }

        const query = `
            SELECT TOP (@limit)
                n.ID,
                n.Title,
                n.Message,
                n.ActionButtonText,
                n.ActionUrl,
                n.Priority,
                n.SenderUserID,
                n.SenderEmployeeID,
                n.ContextData,
                n.ReferenceID,
                n.ReferenceType,
                n.CreatedAt,
                n.ExpiresAt,
                nt.TypeName,
                nt.DisplayName as TypeDisplayName,
                nt.IconName,
                nt.ColorScheme,
                ISNULL(sender_user.user_name, '') as SenderUserName,
                ISNULL(sender_emp.Title, '') as SenderEmployeeName,
                nr.RecipientUserID,
                nr.RecipientRoleID,
                nr.RecipientEmployeeID,
                nr.IsRead,
                nr.ReadAt,
                nr.IsArchived,
                nr.ArchivedAt,
                ISNULL(recipient_user.user_name, '') as RecipientUserName,
                ISNULL(recipient_role.RoleName, '') as RecipientRoleName,
                ISNULL(recipient_emp.Title, '') as RecipientEmployeeName
            FROM Notifications n
            INNER JOIN NotificationTypes nt ON n.TypeID = nt.ID
            LEFT JOIN users sender_user ON n.SenderUserID = sender_user.id
            LEFT JOIN EmployeeDetails sender_emp ON n.SenderEmployeeID = sender_emp.ID
            INNER JOIN NotificationRecipients nr ON n.ID = nr.NotificationID
            LEFT JOIN users recipient_user ON nr.RecipientUserID = recipient_user.id
            LEFT JOIN User_Role recipient_role ON nr.RecipientRoleID = recipient_role.RoleID
            LEFT JOIN EmployeeDetails recipient_emp ON nr.RecipientEmployeeID = recipient_emp.ID
            ${whereClause}
            AND n.CreatedAt >= DATEADD(day, -30, GETDATE())
            AND nt.IsActive = 1
            ORDER BY n.CreatedAt DESC`;

        request.input('userID', sql.NVarChar(32), userID);
        request.input('limit', sql.Int, limit);

        const result = await request.query(query);
        return result.recordset;
    }

    /**
     * Mark notification as read
     */
    async markAsRead(userID, notificationID) {
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            UPDATE NotificationRecipients 
            SET IsRead = 1, ReadAt = GETDATE()
            WHERE NotificationID = @notificationID 
            AND (RecipientUserID = @userID OR RecipientRoleID = (SELECT RoleID FROM users WHERE id = @userID))`;

        request.input('userID', sql.NVarChar(32), userID);
        request.input('notificationID', sql.BigInt, notificationID);

        await request.query(query);

        // Emit notification change event
        ioEmitter.emit('notifyAll', {
            name: 'notifications/changed',
            data: { action: 'archived', notificationID: notificationID }
        });
    }

    /**
     * Mark notification as archived
     */
    async markAsArchived(userID, notificationID) {
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            UPDATE NotificationRecipients 
            SET IsArchived = 1, ArchivedAt = GETDATE()
            WHERE NotificationID = @notificationID 
            AND (RecipientUserID = @userID OR RecipientRoleID = (SELECT RoleID FROM users WHERE id = @userID))`;

        request.input('userID', sql.NVarChar(32), userID);
        request.input('notificationID', sql.BigInt, notificationID);

        await request.query(query);

        // Emit notification change event
        ioEmitter.emit('notifyAll', {
            name: 'notifications/changed',
            data: { action: 'read', notificationID: notificationID }
        });
    }

    /**
     * Get unread notification count for a user
     */
    async getUnreadCount(userID) {
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            SELECT COUNT(*) as UnreadCount
            FROM Notifications n
            INNER JOIN NotificationTypes nt ON n.TypeID = nt.ID
            INNER JOIN NotificationRecipients nr ON n.ID = nr.NotificationID
            WHERE (nr.RecipientUserID = @userID OR nr.RecipientRoleID = (SELECT RoleID FROM users WHERE id = @userID))
            AND nr.IsRead = 0 AND nr.IsArchived = 0
            AND n.CreatedAt >= DATEADD(day, -30, GETDATE())
            AND nt.IsActive = 1`;

        request.input('userID', sql.NVarChar(32), userID);

        const result = await request.query(query);
        return result.recordset[0].UnreadCount;
    }

    /**
     * Create time update notification for admins
     */
    async createTimeUpdateNotification(timeUpdateData) {
        const {
            role,
            employeeName,
            quotationNo,
            clientName,
            newTime,
            reason,
            senderUserID,
            senderEmployeeID,
            purposeID
        } = timeUpdateData;

        const templateName = role === 'delivery' ? 'DELIVERY_TIME_UPDATE' : 'INSTALLER_TIME_UPDATE';
        const formattedTime = new Date(newTime).toLocaleString();

        const context = {
            employeeName,
            quotationNo,
            clientName,
            newTime: formattedTime,
            reason
        };

        const recipients = [{ roleID: 1 }];

        return await this.createNotification({
            templateName,
            context,
            senderUserID,
            senderEmployeeID,
            referenceID: purposeID.toString(),
            referenceType: 'PURPOSE',
            recipients
        });
    }
}

module.exports = new NotificationServiceSimple();
