const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { coa32Columns, coa3Columns, coa321Columns } = require('./utils/constant');
const notificationService = require('../services/notificationService');

router.use(authorization);


router.get('/tasks/:role', async (req, res) => {
    const { role } = req.params;
    if (!role || (role !== 'installer' && role !== 'delivery')) {
        return res.status(400).send('Invalid role');
    }
    let query = `
        SELECT 
            o.*, 
            c.Title AS client_name, 
            c1.add1 AS client_address, 
            c1.email AS client_email, 
            o.ContactPerson AS contact_person,
            c1.Mobile AS client_mobile_no, 
            c1.tel AS client_telephone,
            emp.title AS deliveredBy,
            CASE
                WHEN o.deliveryPersonID IS NOT NULL 
                    AND o.RefVoucherNo IS NOT NULL 
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM Offer_det od
                        WHERE od.Voucher_No = o.RefVoucherNo
                        AND ISNULL(od.DeliveredRemainingItems, 1) > 0
                    ) 
                THEN 'completed'
                WHEN o.deliveryPersonID IS NOT NULL 
                    AND o.RefVoucherNo IS NOT NULL 
                    AND EXISTS (
                        SELECT 1 
                        FROM Offer_det od
                        WHERE od.Voucher_No = o.RefVoucherNo
                        AND od.DeliveredRemainingItems > 0
                    ) 
                THEN 'partial-completed'
                WHEN o.IsApproved = 1 THEN 'in-progress'
                ELSE 'pending'
            END AS status
        FROM 
            ClientPurpose o
        LEFT JOIN 
            Coa32 c ON o.clientID = c.id
        LEFT JOIN 
            Coa321 c1 ON o.clientID = c1.id
        LEFT JOIN 
            EmployeeDetails emp ON o.deliveryPersonID = emp.ID`;
    if (role === 'installer') {
        query = `
            SELECT 
                o.*, 
                c.Title AS client_name, 
                c1.add1 AS client_address, 
                o.ContactPerson AS contact_person,
                c1.email AS client_email, 
                c1.Mobile AS client_mobile_no, 
                c1.tel AS client_telephone,
                emp.title AS installedBy,
                CASE 
                    WHEN o.installerID IS NOT NULL 
                        AND o.RefVoucherNo IS NOT NULL 
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM Offer_det od
                            WHERE od.Voucher_No = o.RefVoucherNo
                            AND ISNULL(od.InstalledRemainingItems, 1) > 0
                        ) 
                    THEN 'completed'
                    WHEN o.installerID IS NOT NULL 
                        AND o.RefVoucherNo IS NOT NULL 
                        AND EXISTS (
                            SELECT 1 
                            FROM Offer_det od
                            WHERE od.Voucher_No = o.RefVoucherNo
                            AND od.InstalledRemainingItems > 0
                        ) 
                    THEN 'partial-completed'
                    WHEN o.deliveryPersonID IS NOT NULL 
                        AND o.RefVoucherNo IS NOT NULL 
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM Offer_det od
                            WHERE od.Voucher_No = o.RefVoucherNo
                            AND ISNULL(od.DeliveredRemainingItems, 1) > 0
                        ) 
                    THEN 'in-progress'
                    ELSE 'pending'
                END AS status
            FROM 
                ClientPurpose o
            LEFT JOIN 
                Coa32 c ON o.clientID = c.id
            LEFT JOIN 
                Coa321 c1 ON o.clientID = c1.id
            LEFT JOIN 
                EmployeeDetails emp ON o.installerID= emp.ID`;
        if (req.user.RoleID !== 1) {
            query += ` WHERE O.installerID = @EmployeeID`;
        } else {
            query += ` WHERE O.installerID IS NOT NULL`;
        }
    } else if (role === 'delivery' && req.user.RoleID !== 1) {
        query += ` WHERE O.deliveryPersonID = @EmployeeID`;
    } else {
        query += ` WHERE O.deliveryPersonID IS NOT NULL`;
    }
    const pool = await getPool();
    const request = new sql.Request(pool);
    if (req.user.RoleID !== 1) {
        request.input('EmployeeID', sql.VarChar, req.user.Emp_ID);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        if (result.recordset.length === 0) { return res.status(404).send('No tasks found.'); }
        res.status(200).json({
            message: 'Tasks fetched successfully',
            data: result.recordset
        });
    });
});

router.post('/', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        const maxIdQuery = `SELECT MAX(C.id) as maxId FROM Coa32 C
            LEFT JOIN Coa3 C3 ON C3.id = C.id
            LEFT JOIN Coa321 C1 ON C1.id = C.id
            WHERE C3.atp2_ID = 'CT'`;
        const maxIdResult = await request.query(maxIdQuery);
        const maxId = maxIdResult.recordset[0].maxId || 0;
        const newId = (Number(maxId) + 1).toString();
        let columnsCoa32 = ['id'];
        let valuesCoa32 = [`@id`];
        let columnsCoa3 = ['id'];
        let valuesCoa3 = [`@id`];
        let columnsCoa321 = ['id'];
        let valuesCoa321 = [`@id`];

        request.input('id', sql.VarChar(32), newId);

        coa3Columns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columnsCoa3.push(name);
                valuesCoa3.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });
        columnsCoa3.push('atp2_ID');
        valuesCoa3.push(`@atp2_ID`);
        request.input('atp2_ID', sql.VarChar, 'CT');

        coa32Columns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columnsCoa32.push(name);
                valuesCoa32.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        coa321Columns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columnsCoa321.push(name);
                valuesCoa321.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const queryCoa3 = `INSERT INTO coa3 (${columnsCoa3.join(', ')}) VALUES (${valuesCoa3.join(', ')})`;
        const queryCoa32 = `INSERT INTO coa32 (${columnsCoa32.join(', ')}) VALUES (${valuesCoa32.join(', ')})`;
        const queryCoa321 = `INSERT INTO coa321 (${columnsCoa321.join(', ')}) VALUES (${valuesCoa321.join(', ')})`;

        await request.query(queryCoa3);
        await request.query(queryCoa32);
        await request.query(queryCoa321);

        await transaction.commit();
        res.status(201).send({
            message: 'Client added successfully',
            id: newId,
            ...req.body
        });
    } catch (err) {
        await transaction.rollback();
        res.status(500).send(err);
    }
});

router.get('/:id?', async (req, res) => {
    const { id } = req.params;
    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        let query = `
            SELECT 
                C.id,
                C.title,
                C1.formalTitle,
                C1.surName,
                C1.firstName,
                C1.add1,
                C1.tel,
                C1.Mobile,
                C1.email,
                C1.url,
                C1.Mode
            FROM 
                coa32 C
            LEFT JOIN 
                coa321 C1 ON C.id = C1.id
        `;

        if (id) {
            query += ` WHERE C.id = @id`;
            request.input('id', sql.VarChar(32), id);
        }

        const result = await request.query(query);

        if (result.recordset.length === 0) {
            return res.status(404).send('Client not found.');
        }

        if (id) {
            const client = result.recordset[0];
            res.status(200).json({
                message: 'Client details retrieved successfully',
                data: {
                    id: client.id,
                    title: client.title,
                    formalTitle: client.formalTitle,
                    surName: client.surName,
                    firstName: client.firstName,
                    add1: client.add1,
                    tel: client.tel,
                    Mobile: client.Mobile,
                    email: client.email,
                    tags: client.Mode,
                    url: client.url,
                }
            });
        } else {
            res.status(200).json({
                message: 'Clients retrieved successfully',
                data: result.recordset
            });
        }
    } catch (err) {
        res.status(500).send(err);
    }
});

router.get('/report/:clientId', async (req, res) => {
    const { clientId } = req.params;
    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        const query = `
            SELECT 
                o.*, 
                c.Title AS client_name, 
                o.ContactPerson AS contact_person,
                c1.add1 AS client_address, 
                c1.email AS client_email, 
                c1.Mobile AS client_mobile_no, 
                c1.tel AS client_telephone,
                E.Title AS EmployeeTitle,
                CASE 
                    WHEN o.chk_id IS NOT NULL AND r.RefVoucherNo IS NOT NULL THEN 'completed'
                    WHEN o.chk_id IS NOT NULL THEN 'in-progress'
                    WHEN o.chk_id IS NULL THEN 'pending'
                END AS status
            FROM 
                Offer o
            LEFT JOIN 
                Coa32 c ON o.client_id = c.id
            LEFT JOIN 
                RecoveryFollowUps r ON r.RefVoucherNo = o.Voucher_No
            LEFT JOIN 
                Coa321 c1 ON o.client_id = c1.id
            LEFT JOIN 
                EmployeeDetails E ON E.ID = o.EmployeeID
            WHERE o.client_id = @clientId`;

        request.input('clientId', sql.VarChar, clientId);
        const result = await request.query(query);

        if (result.recordset.length === 0) {
            return res.status(404).send('No offers found for the given client ID.');
        }

        res.status(200).json({
            message: 'Offers fetched successfully',
            data: result.recordset
        });
    } catch (err) {
        res.status(500).send(err);
    }
});

router.put('/update-time/:role/:purposeId', async (req, res) => {
    const { role, purposeId } = req.params;
    const { newTime, reason } = req.body;

    if (!role || (role !== 'delivery' && role !== 'installer')) {
        return res.status(400).json({ message: 'Invalid role. Must be "delivery" or "installer".' });
    }

    if (!newTime || !reason) {
        return res.status(400).json({ message: 'New time and reason are required.' });
    }

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();

        const timeField = role === 'delivery' ? 'deliveryPersonTime' : 'installerTime';
        const reasonField = role === 'delivery' ? 'deliveryTimeUpdateReason' : 'installerTimeUpdateReason';
        const updateRequest = new sql.Request(transaction);
        const updateQuery = `
            UPDATE ClientPurpose
            SET ${timeField} = @newTime, ${reasonField} = @reason
            WHERE ID = @purposeId`;

        updateRequest.input('newTime', sql.DateTime, new Date(newTime));
        updateRequest.input('reason', sql.VarChar(500), reason);
        updateRequest.input('purposeId', sql.Int, purposeId);

        const updateResult = await updateRequest.query(updateQuery);

        if (updateResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Task not found or no changes made.' });
        }

        const detailsRequest = new sql.Request(transaction);
        const detailsQuery = `
            SELECT
                cp.RefVoucherNo,
                c.Title as client_name,
                emp.Title as employee_name
            FROM ClientPurpose cp
            LEFT JOIN Coa32 c ON cp.clientID = c.id
            LEFT JOIN EmployeeDetails emp ON cp.${role === 'delivery' ? 'deliveryPersonID' : 'installerID'} = emp.ID
            WHERE cp.ID = @purposeId`;

        detailsRequest.input('purposeId', sql.Int, purposeId);
        const detailsResult = await detailsRequest.query(detailsQuery);

        if (detailsResult.recordset.length > 0) {
            const taskDetails = detailsResult.recordset[0];

            try {
                await notificationService.createTimeUpdateNotification({
                    role,
                    employeeName: taskDetails.employee_name || 'Unknown Employee',
                    quotationNo: taskDetails.RefVoucherNo || 'Unknown',
                    clientName: taskDetails.client_name || 'Unknown Client',
                    newTime,
                    reason,
                    senderUserID: req.user.id,
                    senderEmployeeID: req.user.Emp_ID,
                    purposeID: purposeId
                });
            } catch (notificationError) {
                console.error('Error creating notification:', notificationError);
            }
        }

        await transaction.commit();

        res.status(200).json({
            message: `${role.charAt(0).toUpperCase() + role.slice(1)} time updated successfully`,
            data: {
                purposeId,
                newTime,
                reason,
                role
            }
        });
    } catch (err) {
        await transaction.rollback();
        console.error('Error updating time:', err);
        res.status(500).json({ message: 'Internal server error', error: err.message });
    }
});

module.exports = router;
