import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  Popover,
  PopoverTrigger,
  <PERSON>overContent,
  PopoverHeader,
  PopoverBody,
  PopoverCloseButton,
  Badge,
  VStack,
  Text,
  Button,
  Divider,
  useToast,
  <PERSON>ltip,
  <PERSON><PERSON><PERSON>ck,
  Spinner
} from '@chakra-ui/react';
import { FiBell, FiCheck, FiArchive, FiExternalLink } from 'react-icons/fi';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getSocket } from '../../hooks/useSocket';
import useBrowserNotifications from '../../hooks/useBrowserNotifications';

dayjs.extend(relativeTime);

const NotificationBell = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const toast = useToast();
  const router = useRouter();
  const { showTimeUpdateAlert } = useBrowserNotifications();

  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('notifications?limit=10&includeRead=true&includeArchived=false');
      setNotifications(response.data.data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const response = await axiosInstance.get('notifications/unread-count');
      setUnreadCount(response.data.data.unreadCount);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await axiosInstance.put(`notifications/${notificationId}/read`);
      setNotifications(prev =>
        prev.map(notif =>
          notif.ID === notificationId
            ? { ...notif, IsRead: true, ReadAt: new Date().toISOString() }
            : notif
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: "Error",
        description: "Failed to mark notification as read",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const markAsArchived = async (notificationId) => {
    try {
      await axiosInstance.put(`notifications/${notificationId}/archive`);
      setNotifications(prev => prev.filter(notif => notif.ID !== notificationId));
      if (!notifications.find(n => n.ID === notificationId)?.IsRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      toast({
        title: "Notification archived",
        status: "success",
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error archiving notification:', error);
      toast({
        title: "Error",
        description: "Failed to archive notification",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const markAllAsRead = async () => {
    try {
      await axiosInstance.put('notifications/mark-all-read');
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, IsRead: true, ReadAt: new Date().toISOString() }))
      );
      setUnreadCount(0);
      toast({
        title: "All notifications marked as read",
        status: "success",
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleNotificationAction = (notification) => {
    if (!notification.IsRead) {
      markAsRead(notification.ID);
    }
    if (notification.ActionUrl) {
      router.push(notification.ActionUrl);
      setIsOpen(false);
    }
  };

  const getNotificationColor = (colorScheme) => {
    const colorMap = {
      blue: 'blue.500',
      green: 'green.500',
      orange: 'orange.500',
      red: 'red.500',
      purple: 'purple.500',
      yellow: 'yellow.500'
    };
    return colorMap[colorScheme] || 'gray.500';
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 4: return 'red.500';
      case 3: return 'orange.500';
      case 2: return 'blue.500';
      case 1: return 'gray.500';
      default: return 'gray.500';
    }
  };
  useEffect(() => {
    const socket = getSocket();
    fetchUnreadCount();

    const handleTimeUpdate = (data) => {
      fetchUnreadCount();
      showTimeUpdateAlert(data);
    };

    socket.on("notifications/changed", handleTimeUpdate);

    return () => {
      socket.off("notifications/changed", handleTimeUpdate);
    };
  }, []);

  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen]);

  return (
    <Popover isOpen={isOpen} onClose={() => setIsOpen(false)} placement="bottom-end">
      <PopoverTrigger>
        <Box position="relative">
          <Tooltip label="Notifications" hasArrow>
            <IconButton
              icon={<FiBell />}
              variant="ghost"
              colorScheme="gray"
              onClick={() => setIsOpen(!isOpen)}
              aria-label="Notifications"
            />
          </Tooltip>
          {unreadCount > 0 && (
            <Badge
              colorScheme="red"
              borderRadius="full"
              position="absolute"
              top="-1"
              right="-1"
              fontSize="xs"
              minW="20px"
              h="20px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Box>
      </PopoverTrigger>

      <PopoverContent w="400px" maxH="500px" overflowY="auto">
        <PopoverHeader paddingX={8}>
          <HStack justify="space-between">
            <Text fontWeight="bold">Notifications</Text>
            {unreadCount > 0 && (
              <Button size="xs" variant="ghost" onClick={markAllAsRead}>
                Mark all read
              </Button>
            )}
          </HStack>
        </PopoverHeader>
        <PopoverCloseButton />

        <PopoverBody p={0}>
          {isLoading ? (
            <Box p={4} textAlign="center">
              <Spinner size="sm" />
              <Text mt={2} fontSize="sm" color="gray.500">Loading notifications...</Text>
            </Box>
          ) : notifications.length === 0 ? (
            <Box p={4} textAlign="center">
              <Text fontSize="sm" color="gray.500">No notifications</Text>
            </Box>
          ) : (
            <VStack spacing={0} align="stretch">
              {notifications.map((notification, index) => (
                <Box key={notification.ID}>
                  <Box
                    p={3}
                    bg={notification.IsRead ? 'white' : 'blue.50'}
                    _hover={{ bg: 'gray.50' }}
                    cursor="pointer"
                    onClick={() => handleNotificationAction(notification)}
                  >
                    <HStack align="start" spacing={3}>
                      <Box
                        w="8px"
                        h="8px"
                        borderRadius="full"
                        bg={getPriorityColor(notification.Priority)}
                        mt={1}
                        flexShrink={0}
                      />

                      <VStack align="start" spacing={1} flex={1}>
                        <Text fontSize="sm" fontWeight={notification.IsRead ? "normal" : "semibold"}>
                          {notification.Title}
                        </Text>
                        <Text fontSize="xs" color="gray.600" noOfLines={2}>
                          {notification.Message}
                        </Text>
                        <HStack spacing={2}>
                          <Text fontSize="xs" color="gray.500">
                            {dayjs(notification.CreatedAt).fromNow()}
                          </Text>
                          {notification.ActionUrl && (
                            <FiExternalLink size="12" color="gray" />
                          )}
                        </HStack>
                      </VStack>

                      <VStack spacing={1}>
                        {!notification.IsRead && (
                          <Tooltip label="Mark as read" hasArrow>
                            <IconButton
                              icon={<FiCheck />}
                              size="xs"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                markAsRead(notification.ID);
                              }}
                              aria-label="Mark as read"
                            />
                          </Tooltip>
                        )}
                        <Tooltip label="Archive" hasArrow>
                          <IconButton
                            icon={<FiArchive />}
                            size="xs"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              markAsArchived(notification.ID);
                            }}
                            aria-label="Archive"
                          />
                        </Tooltip>
                      </VStack>
                    </HStack>
                  </Box>
                  {index < notifications.length - 1 && <Divider />}
                </Box>
              ))}
            </VStack>
          )}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationBell;
