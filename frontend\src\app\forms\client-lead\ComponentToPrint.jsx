import { Box, Flex, Text, Table, Thead, Tbody, Tr, Th, Td, Button, Input, Grid, GridItem, Spacer } from "@chakra-ui/react";
import { FaCcPaypal, FaCcVisa } from "react-icons/fa";
import logo from '@assets/assets/imgs/logo.png';
import quotation_payment from '@assets/assets/imgs/quotation_payment.png';
import Image from "next/image";
import dayjs from "dayjs";

const ComponentToPrint = ({ innerRef, data }) => {
    return (
        <Box ref={innerRef} p={8} maxW="800px" mx="auto">
            <Flex justify="space-between" align="center" mb={6} borderBottom={'1px solid'} pb={4}>
                <Box>
                    <Image src={logo} alt="Linkers Communication" height={50} />
                </Box>
                <Box display={'flex'} flexDirection={'column'} alignItems={'flex-end'} justifyContent={'flex-end'}>
                    <Text fontSize={30} fontWeight="bold">QUOTATION</Text>
                    <Text color="red" fontWeight="bold">EAM {data?.vno ?? 'N/A'}</Text>
                </Box>
            </Flex>

            <Flex justify="space-between" align="start" mb={6}>
                <Box mb={6}>
                    <Text fontSize={12} color="gray">INVOICE TO</Text>
                    <Text>{data?.clientTitle ?? 'N/A'}</Text>
                    <Text>{data?.email ?? 'N/A'}</Text>
                    <Text>{data?.phoneNumber ?? 'N/A'}</Text>
                    <Text>{data?.address ?? 'N/A'}</Text>
                </Box>
                <Box textAlign="right">
                    <Text fontSize="lg" fontWeight="bold">Eco Assets Manager PTY LTD</Text>
                    <Text fontWeight="bold">ABN **************</Text>
                    <Text color="gray">3/36 Zakwell Court</Text>
                    <Text color="gray">Coolaroo 3048 VIC</Text>
                    <Text fontWeight="bold">1300 5333 733</Text>
                </Box>
            </Flex>


            <Table variant="simple" mb={6}>
                <Thead>
                    <Tr sx={{ backgroundColor: '#3a866a !important' }}>
                        <Th color="white">Products</Th>
                        <Th color="white">Quantity</Th>
                        <Th color="white">Price</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    {data?.items.map((item, index) => {
                        return (
                            <Tr key={index}>
                                <Td fontWeight={'bold'}>{item?.Item_Title ?? 'N/A'}</Td>
                                <Td fontWeight={'bold'}>{parseFloat(item?.Qty).toFixed(2) ?? 'N/A'}</Td>
                                <Td fontWeight={'bold'}>${parseFloat(item?.Total).toFixed(2) ?? 'N/A'}</Td>
                            </Tr>
                        )
                    })}
                </Tbody>
            </Table>

            <Text mb={'50px !important'} fontSize="sm" color="gray">* Additional charges may apply subject to installer’s assessment</Text>
            <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                <Box>
                    <Image src={quotation_payment} alt="Quotation Payment" style={{ marginBottom: 20 }}></Image>
                    <Text fontSize="lg" fontWeight="bold" color={'gray.600'}>PAYMENT METHOD</Text>
                    <Text fontWeight="bold" fontSize={12} color={'gray'}>Bank Name: Bank of Melbourne</Text>
                    <Text fontWeight="bold" fontSize={12} color={'gray'}>BSB: 193879</Text>
                    <Text fontWeight="bold" fontSize={12} color={'gray'}>Account #: *********</Text>
                    <Text fontSize="sm" color="gray" marginTop={'12px !important'}>* Please deposit 10% and balance must be paid on installation day.</Text>
                </Box>
                <Box bg="gray.100" p={4} borderRadius="md">
                    <Flex justify="space-between">
                        <Text color={'gray.700'}>Subtotal Incl. GST</Text>
                        <Text color={'black'}>${parseFloat(data?.netAmount).toFixed(2) ?? 0.00}</Text>
                    </Flex>
                    <Flex sx={{ fontWeight: 600, color: '#3a866a' }} justify="space-between">
                        <Text>EAM Discount</Text>
                        <Text>-$0.00</Text>
                    </Flex>
                    <Flex justify="space-between" mb={5}>
                        <Text color={'gray.700'}>Tax Included ({(data?.salesTaxR ?? 0) + (data?.salesTaxA ?? 0)}%)</Text>
                        <Text color={'black'}>${parseFloat(data?.sTaxAmount).toFixed(2) ?? 0}</Text>
                    </Flex>
                    <Flex justify="space-between" fontWeight="bold" sx={{ fontWeight: 600, color: '#3a866a' }}>
                        <Text>VEEC Discount</Text>
                        <Text>${parseFloat(data?.discountAmount).toFixed(2) ?? 0.00}</Text>
                    </Flex>
                    <Flex justify="space-between" sx={{ fontWeight: 600, color: '#3a866a' }}>
                        <Text>STC Discount</Text>
                        <Text>N/A</Text>
                    </Flex>
                    <Flex justify="space-between" sx={{ fontWeight: 600, color: '#3a866a' }}>
                        <Text>SOLARVIC Rebate</Text>
                        <Text>N/A</Text>
                    </Flex>
                    <Flex marginTop={5} justify="space-between" fontSize="xl" fontWeight="bold">
                        <Box >
                            <Text fontSize={18} lineHeight={'15px !important'}>Total Out of Pocket</Text>
                            <Text fontSize={10}>Incl. GST</Text>
                        </Box>
                        <Text>${parseFloat(data?.netPayableAmt).toFixed(2) ?? 0.00}</Text>
                    </Flex>
                </Box>
            </Grid>

            <Flex marginTop={'100px !important'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Flex flexDirection={'column'} alignItems={'center'}>
                        <img src={data?.signature} alt="Signature" width={100} height={100}/>
                        <Text>__________________________________</Text>
                    </Flex>
                    <Text fontWeight={'bold'} marginTop={'5px !important'} color={'black'}>Customer Signature</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'} justifyContent={'flex-end'}>
                    <Text>{dayjs().format("DD-MMM-YYYY")}</Text>
                    <Text>_____________________</Text>
                    <Text fontWeight={'bold'} marginTop={'5px !important'} color={'black'}>Date</Text>
                </Flex>
            </Flex>
        </Box>
    );
}

export default ComponentToPrint;
