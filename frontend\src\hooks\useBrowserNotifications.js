import { useState, useEffect, useCallback } from 'react';

const useBrowserNotifications = () => {
  const [permission, setPermission] = useState('default');
  const [isSupported, setIsSupported] = useState(false);

  // Check if browser supports notifications
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setIsSupported(true);
      setPermission(Notification.permission);
    }
  }, []);

  // Request notification permission
  const requestPermission = useCallback(async () => {
    if (!isSupported) {
      console.log('Browser notifications not supported');
      return false;
    }

    if (permission === 'granted') {
      return true;
    }

    if (permission !== 'denied') {
      try {
        const result = await Notification.requestPermission();
        setPermission(result);
        return result === 'granted';
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        return false;
      }
    }

    return false;
  }, [isSupported, permission]);

  // Show browser notification
  const showNotification = useCallback((title, options = {}) => {
    if (!isSupported || permission !== 'granted') {
      console.log('Cannot show notification: permission not granted or not supported');
      return null;
    }

    try {
      const notification = new Notification(title, {
        icon: '/icon512_rounded.png',
        badge: '/icon512_rounded.png',
        tag: 'impex-grace-notification',
        renotify: true,
        requireInteraction: false,
        ...options
      });

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      // Handle notification click
      notification.onclick = () => {
        window.focus();
        notification.close();
        if (options.onClick) {
          options.onClick();
        }
      };

      return notification;
    } catch (error) {
      console.error('Error showing notification:', error);
      return null;
    }
  }, [isSupported, permission]);

  // Show notification for new notifications
  const showNewNotificationAlert = useCallback((data = {}) => {
    return showNotification('New Notification', {
      body: 'You have received a new notification',
      icon: '/icon512_rounded.png',
      tag: `notification-${data.notificationID || Date.now()}`,
      onClick: () => {
        // Focus on the browser window when notification is clicked
        if (window.focus) window.focus();
      }
    });
  }, [showNotification]);

  // Show notification for time updates
  const showTimeUpdateAlert = useCallback((data = {}) => {
    const { employeeName, clientName, quotationNo, newTime, reason } = data.data.context;
    const body = `${employeeName} has updated the time for quotation ${quotationNo} to ${newTime}. Reason: ${reason}`;
    return showNotification('Time Update', {
      body: body,
      icon: '/icon512_rounded.png',
      tag: `time-update-${data.purposeId || Date.now()}`,
      onClick: () => {
        if (window.focus) window.focus();
      }
    });
  }, [showNotification]);

  // Auto-request permission on first use
  useEffect(() => {
    if (isSupported && permission === 'default') {
      // Automatically request permission when hook is first used
      requestPermission();
    }
  }, [isSupported, permission, requestPermission]);

  return {
    // State
    permission,
    isSupported,
    isGranted: permission === 'granted',
    
    // Functions
    requestPermission,
    showNotification,
    showNewNotificationAlert,
    showTimeUpdateAlert
  };
};

export default useBrowserNotifications;
